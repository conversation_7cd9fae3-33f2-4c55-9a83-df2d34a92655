import 'package:flutter/material.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class AccountInfoListItem extends StatelessWidget {
  final Widget icon;
  final String title;
  final String? subtitle;
  final VoidCallback? onTap;
  final bool showArrow;
  final Color? titleColor;

  const AccountInfoListItem({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.onTap,
    this.showArrow = true,
    this.titleColor,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          children: [
            // Icon
            SizedBox(width: 24, height: 24, child: icon),
            const SizedBox(width: 16),
            // Title and subtitle
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: titleMedium.copyWith(
                      color: titleColor ?? themeData.neutral800,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle!,
                      style: bodyMedium.copyWith(
                        color: themeData.neutral400,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            // Arrow
            if (showArrow)
              SizedBox(
                width: 24,
                height: 24,
                child: Assets.icons.icArrowRightAccount.svg(
                  colorFilter: ColorFilter.mode(
                    themeData.neutral400,
                    BlendMode.srcIn,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
