import 'package:flutter/material.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class AccountInfoListItem extends StatelessWidget {
  final Widget icon;
  final String title;
  final String? subtitle;
  final VoidCallback? onTap;
  final bool showArrow;
  final Color? titleColor;

  const AccountInfoListItem({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.onTap,
    this.showArrow = true,
    this.titleColor,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          children: [
            // Icon
            SizedBox(width: 24, height: 24, child: icon),
            const SizedBox(width: 16),
            // Title
            Expanded(
              child: Text(
                title,
                style: titleMedium.copyWith(
                  color: titleColor ?? const Color(0xFF292929),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            // Subtitle (right aligned)
            if (subtitle != null) ...[
              Text(
                subtitle!,
                style: bodyMedium.copyWith(
                  color: const Color(0xFF777777),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
            // Arrow
            if (showArrow) ...[
              const SizedBox(width: 8),
              SizedBox(
                width: 24,
                height: 24,
                child: Assets.icons.icArrowRightAccount.svg(
                  colorFilter: const ColorFilter.mode(
                    Color(0xFF777777),
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
