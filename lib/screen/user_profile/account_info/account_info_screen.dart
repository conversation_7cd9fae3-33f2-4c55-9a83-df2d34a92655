import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/screen/user_profile/account_info/widget/account_info_list_item.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class AccountInfoScreen extends StatelessWidget {
  const AccountInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: const Color(0xFF1A202C),
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        leading: Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.05),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Color(0xFF292929),
              size: 16,
            ),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        title: Text(
          'Account',
          style: titleLarge.copyWith(
            color: const Color(0xFF292929),
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // User Profile Section with Avatar
          Container(
            margin: const EdgeInsets.all(16),
            child: Stack(
              children: [
                // Avatar with indicator
                Container(
                  width: 60,
                  height: 60,
                  decoration: const BoxDecoration(shape: BoxShape.circle),
                  child: Stack(
                    children: [
                      ClipOval(
                        child: Assets.images.avatarSample.image(
                          fit: BoxFit.cover,
                          width: 60,
                          height: 60,
                        ),
                      ),
                      // Online indicator
                      Positioned(
                        bottom: 2,
                        right: 2,
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: const Color(0xFF4CAF50),
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Account Information List
          Expanded(
            child: Column(
              children: [
                AccountInfoListItem(
                  icon: Assets.icons.icProfile.svg(
                    colorFilter: const ColorFilter.mode(
                      Color(0xFF777777),
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Username',
                  subtitle: 'Alice2432',
                  onTap: () {
                    // TODO: Navigate to username edit
                  },
                ),
                AccountInfoListItem(
                  icon: Assets.icons.icSms.svg(
                    colorFilter: const ColorFilter.mode(
                      Color(0xFF777777),
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Email',
                  subtitle: '<EMAIL>',
                  showArrow: false,
                  onTap: () {
                    // TODO: Navigate to email edit
                  },
                ),
                AccountInfoListItem(
                  icon: Assets.icons.icCalendar.svg(
                    colorFilter: const ColorFilter.mode(
                      Color(0xFF777777),
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Birthday',
                  subtitle: 'Update',
                  onTap: () {
                    // TODO: Navigate to birthday edit
                  },
                ),
                AccountInfoListItem(
                  icon: Assets.icons.icLock2Svg_.svg(
                    colorFilter: const ColorFilter.mode(
                      Color(0xFF777777),
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Change password',
                  onTap: () {
                    // TODO: Navigate to change password
                  },
                ),
                AccountInfoListItem(
                  icon: Assets.icons.icLocation.svg(
                    colorFilter: const ColorFilter.mode(
                      Color(0xFF777777),
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Location',
                  subtitle: 'Việt Nam',
                  onTap: () {
                    // TODO: Navigate to location edit
                  },
                ),
                AccountInfoListItem(
                  icon: Assets.icons.icCloseCircle.svg(
                    colorFilter: const ColorFilter.mode(
                      Color(0xFFD33636),
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Delete account',
                  titleColor: const Color(0xFFD33636),
                  onTap: () {
                    // TODO: Handle delete account
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
