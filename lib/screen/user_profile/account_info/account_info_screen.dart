import 'package:flutter/material.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/screen/user_profile/account_info/widget/account_info_list_item.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class AccountInfoScreen extends StatelessWidget {
  const AccountInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Scaffold(
      backgroundColor: themeData.neutral100,
      appBar: BaseAppBar(
        title: 'Account',
        backgroundColor: themeData.neutral800,
        foregroundColor: themeData.neutral800,
        styleTitle: titleLarge.copyWith(
          color: themeData.neutral800,
          fontWeight: FontWeight.w600,
        ),
      ),
      body: Column(
        children: [
          const SizedBox(height: 16),
          // User Profile Section
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                // Avatar
                Container(
                  width: 40,
                  height: 40,
                  decoration: const BoxDecoration(shape: BoxShape.circle),
                  child: ClipOval(
                    child: Assets.images.avatarSample.image(fit: BoxFit.cover),
                  ),
                ),
                const SizedBox(width: 12),
                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Alice',
                        style: titleMedium.copyWith(
                          color: themeData.neutral800,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Alice2432',
                        style: labelMedium.copyWith(
                          color: themeData.neutral400,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Account Information List
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  AccountInfoListItem(
                    icon: Assets.icons.icProfile.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.neutral400,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Username',
                    subtitle: 'Alice2432',
                    onTap: () {
                      // TODO: Navigate to username edit
                    },
                  ),
                  AccountInfoListItem(
                    icon: Assets.icons.icSms.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.neutral400,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Email',
                    subtitle: '<EMAIL>',
                    showArrow: false,
                    onTap: () {
                      // TODO: Navigate to email edit
                    },
                  ),
                  AccountInfoListItem(
                    icon: Assets.icons.icCalendar.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.neutral400,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Birthday',
                    subtitle: 'Update',
                    onTap: () {
                      // TODO: Navigate to birthday edit
                    },
                  ),
                  AccountInfoListItem(
                    icon: Assets.icons.icLock2Svg_.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.neutral400,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Change password',
                    onTap: () {
                      // TODO: Navigate to change password
                    },
                  ),
                  AccountInfoListItem(
                    icon: Assets.icons.icLocation.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.neutral400,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Location',
                    subtitle: 'Việt Nam',
                    onTap: () {
                      // TODO: Navigate to location edit
                    },
                  ),
                  AccountInfoListItem(
                    icon: Assets.icons.icCloseCircle.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.red500,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Delete account',
                    titleColor: themeData.red500,
                    onTap: () {
                      // TODO: Handle delete account
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
